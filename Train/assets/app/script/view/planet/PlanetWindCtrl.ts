import CoreEventType from '../../../core/event/CoreEventType';
import { MAX_VALUE, MAX_ZINDEX } from "../../common/constant/Constant";
import { BattleLevelType, ConditionType, HeroAction, PlanetMineGameType, PlanetNodeType, UIFunctionType } from '../../common/constant/Enums';
import EventType from "../../common/event/EventType";
import NodeType from '../../common/event/NodeType';
import { animHelper } from "../../common/helper/AnimHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { mapHelper } from "../../common/helper/MapHelper";
import { resHelper } from '../../common/helper/ResHelper';
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../../model/common/ConditionObj";
import HeroModel from "../../model/hero/HeroModel";
import PlanetCheckPointModel from "../../model/planet/PlanetCheckPointModel";
import PlanetEmptyNode from '../../model/planet/PlanetEmptyNode';
import PlanetMap from "../../model/planet/PlanetMap";
import PlanetMineModel from "../../model/planet/PlanetMineModel";
import PlanetModel from "../../model/planet/PlanetModel";
import PlanetNodeModel from '../../model/planet/PlanetNodeModel';
import HeroCmpt from "../cmpt/hero/HeroCmpt";
import PlanetCheckPointCmpt from "../cmpt/planet/PlanetCheckPointCmpt";
import PlanetMineCmpt from "../cmpt/planet/PlanetMineCmpt";
import PlanetNodeCmpt from "../cmpt/planet/PlanetNodeCmpt";
import PlanetNodeRewardCmpt from "../cmpt/planet/PlanetNodeRewardCmpt";
import PlanetNodeRewardGroupCmpt from "../cmpt/planet/PlanetNodeRewardGroupCmpt";
import PlanetQTECmpt from '../cmpt/planet/PlanetQTECmpt';
import PlanetWindLoopNodeCmpt from '../cmpt/planet/PlanetWindLoopNodeCmpt';
import PlanetQuestionModel from '../../model/planet/PlanetQuestionModel';
import PlanetCloudCmpt from '../cmpt/planet/PlanetCloudCmpt';
import { unlockHelper } from '../../common/helper/UnlockHelper';

const { ccclass } = cc._decorator;

const CLOUD_MOVE_SPEED = 10
@ccclass
export default class PlanetWindCtrl extends mc.BaseWindCtrl {

    //@autocode property begin
    protected touchNode_: cc.Node = null // path://touch_n
    protected rootNode_: cc.Node = null // path://root_n
    protected bgSwitchNode_: cc.Node = null // path://root_n/bgSwitch_n
    protected mapNode_: cc.Node = null // path://root_n/map_n
    protected fgSwitchNode_: cc.Node = null // path://root_n/fgSwitch_n
    protected topLayerNode_: cc.Node = null // path://topLayer_n
    //@end

    protected camera: cc.Camera = null
    protected model: PlanetModel = null
    protected hero: HeroModel = null
    protected heroCmpt: HeroCmpt = null

    protected viewNodes: cc.Node[] = []
    protected heroNode_: cc.Node = null;
    protected mineNode_: cc.Node = null;
    protected checkPointNode_: cc.Node = null;
    protected portalNode_: cc.Node = null;
    protected questionNode_: cc.Node = null
    protected nodeRewardNode_: cc.Node = null
    protected gameNode_: cc.Node = null

    protected waitCameraStopPromise: Promise<any> = null
    protected waitCameraStopCallback: Function = null
    protected cameraTarget: any = null

    private uiNode: cc.Node = null
    private qteNode: cc.Node = null
    private hitTipsNode: cc.Node = null

    private viewIndex: number = -1

    protected orgMapNode: cc.Node = null
    protected dropItems: cc.Node[] = []

    protected cloudTime: number = 0

    public listenEventMaps() {
        return [
            { [EventType.ENTER_COLLECT]: this.enterCollect },
            { [EventType.EXIT_COLLECT]: this.exitCollect },
            { [EventType.ENTER_BATTLE]: this.enterBattle },
            { [EventType.EXIT_BATTLE]: this.exitBattle },
            { [EventType.DO_WHEN_UI_SHOW]: this.stopActGenReward },
            { [EventType.CHANGE_PLANET_MAP]: this.changePlanetMap },
            { [EventType.PLANET_NODE_COMPLETE]: this.onNodeComplete },
            { [EventType.PLANET_GEN_REWARD]: this.genRewardNodes },
            { [EventType.PLANET_SHOW_HIT_TIPS]: this.showHitTips },
            { [EventType.RELOAD_PLANET_WIND]: this.onReload },
            { [CoreEventType.PNL_ENTER]: this.onPnlEnter },
            { [CoreEventType.PNL_LEAVE]: this.onPnlLeave },
            { [EventType.ENTER_PLANET_QUESTION]: this.enterQuestion },
            { [EventType.EXIT_PLANET_QUESTION]: this.exitQuestion },
            { [EventType.PLANET_DONE]: this.onDone },

            //guide node
            { [NodeType.GUIDE_CUR_PLANET_NODE]: this.getCurGuideNode },
        ]
    }

    public async onCreate(data) {
        this.camera = cc.find("Canvas/PlanetCamera").Component(cc.Camera)
        this.camera.node.y = 0
        this.camera.zoomRatio = 1

        this.orgMapNode = cc.instantiate2(this.mapNode_, this.mapNode_.parent)
        this.orgMapNode.name = "orgMapNode"

        this.touchNode_.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this)

        this.fixHeight()

        this.model = gameHelper.planet.getCurPlanet()
        let nodes = this.getCurMap().getUnCompleteNodes()
        for (let node of nodes) {
            node.updateContorlFlag()
        }

        let tasks = this.getPreloadTask()
        await Promise.all(tasks)

        this.mapNode_.Component(cc.Widget)?.updateAlignment()
        this.heroCmpt = this.heroNode_.Component(HeroCmpt)

        await gameHelper.planet.land()
    }

    protected getPreloadTask() {
        let tasks = [
            viewHelper.preloadPnl("common/PlanetUI"),
            viewHelper.preloadPnl("planet/PlanetTipsPnl"),
            this.loadUI(),
            this.loadMapPrefabs(),
            this.loadBg(),
            this.loadFg(),
        ]
        if (this.getCurMap().needLandAnim()) {
            tasks.push(viewHelper.preloadPnl("planet/PlanetLandAnimPnl"))
        }
        return tasks
    }

    protected async loadBg() {
        let map = this.getCurMap()
        let mapId = map.getId()
        let path = ""
        if (map.getBranch()) {
            path = `${this.model.getId()}/branch/bg-${mapId}`
        }
        else {
            path = `${this.model.getId()}/bg-${mapId}`
        }
        let fullPath = `tmp/prefab/planet/` + path
        if (!assetsMgr.getInfoWithPath("resources", fullPath)) return
        let pfb = await assetsMgr.loadTempRes(fullPath, cc.Prefab, this.getTag())
        cc.instantiate2(pfb, this.bgSwitchNode_)
    }

    protected async loadFg() {
        let map = this.getCurMap()
        let mapId = map.getId()
        let path = ""
        if (map.getBranch()) {
            path = `${this.model.getId()}/branch/fg-${mapId}`
        }
        else {
            path = `${this.model.getId()}/fg-${mapId}`
        }
        let fullPath = `tmp/prefab/planet/` + path
        if (!assetsMgr.getInfoWithPath("resources", fullPath)) return
        let pfb = await assetsMgr.loadTempRes(fullPath, cc.Prefab, this.getTag())
        cc.instantiate2(pfb, this.fgSwitchNode_)
    }

    protected updateBg(dt) {
        let map = this.getCurMap()
        let bgNode = this.bgSwitchNode_.Child(`bg-${map.getId()}`)
        if (bgNode) {
            this.updateGround(bgNode, dt)
        }
        if (this.fgSwitchNode_) {
            let fgNode = this.fgSwitchNode_.Child(`fg-${map.getId()}`)
            if (fgNode) {
                this.updateGround(fgNode, dt)
            }
        }
    }

    private updateGround(bgNode, dt) {
        let groundNode = bgNode.Child("ground") || bgNode
        let cameraInfo = this.getCameraInfo(bgNode)
        let lb = cameraInfo.lb, rt = cameraInfo.rt
        let left = lb.x, bottom = lb.y, right = rt.x, top = rt.y
        this.loopNode(groundNode, cameraInfo, null, dt)

        let loopNodes = bgNode.Child('loopNodes')
        if (loopNodes) {
            for (let node of loopNodes.children) {
                this.loopNode(node, cameraInfo, null, dt)
            }
        }

        let sky = bgNode.Child('sky')
        if (sky) {
            let minY = -sky.anchorY * sky.height + sky.y
            if (top > minY) {
                sky.active = true
                let width = rt.x - lb.x
                sky.width = width
                sky.x = lb.x + width * sky.anchorX
            }
            else {
                sky.active = false
            }
        }

        let cloud = bgNode.Child("cloud")
        if (cloud && cloud.activeInHierarchy) {
            this.moveCloud(cloud, dt)
        }
    }

    protected loopNode(loopNode, cameraInfo?, maxX?: number, dt: number = 0) {
        let item = loopNode.Child('group')
        if (!item) return
        let loopLayout: cc.Layout = loopNode.Component(cc.Layout)
        if (loopLayout) {
            loopLayout.enabled = false
        }
        this.cloudTime += dt
        let loopCmpt: PlanetWindLoopNodeCmpt = loopNode.Component(PlanetWindLoopNodeCmpt)
        if (!!loopCmpt) {
            let x = ut.convertToNodeAR(this.camera.node, loopNode.parent, cc.v2(-cc.winSize.width * 0.5 / this.camera.zoomRatio, 0)).x
            loopNode.x = x * loopCmpt.parameter1 - this.cloudTime * loopCmpt.parameter2
        }

        cameraInfo = this.getCameraInfo(loopNode)
        let { lb, rt } = cameraInfo
        let left = lb.x, bottom = lb.y, right = rt.x, top = rt.y
        let itemLayout = item.Component(cc.Layout)
        let spacingX = 0
        let paddingLeft = 0
        if (itemLayout) {
            itemLayout.updateLayout()
            itemLayout.enabled = false
            spacingX = itemLayout.spacingX
            paddingLeft = itemLayout.paddingLeft
        }
        let itemWidth = item.width || cc.winSize.width

        let calcIndex = (val) => {
            return Math.floor((val - paddingLeft + spacingX) / (itemWidth + spacingX))
        }

        let leftIndex = calcIndex(left)
        let rightIndex = calcIndex(right)

        if (spacingX < 0 && leftIndex == rightIndex) { //处理重叠的情况
            rightIndex = Math.floor((right - paddingLeft) / (itemWidth + spacingX))
        }

        leftIndex = Math.max(0, leftIndex)

        let childIndex = 0
        for (let i = leftIndex; i <= rightIndex; i++, childIndex++) {
            let x = paddingLeft + (i + item.anchorX) * itemWidth + i * spacingX
            if (maxX && x + ((1 - item.anchorX) * itemWidth) >= maxX) {
                break
            }
            let node = loopNode.children[childIndex]
            if (!node) {
                node = cc.instantiate2(item, loopNode)
                if (node.Component(PlanetCloudCmpt)) {
                    node.Component(PlanetCloudCmpt).reset()
                }
            }
            else {
                if (node.opacity == 0) {
                    if (node.Component(PlanetCloudCmpt)) {
                        node.Component(PlanetCloudCmpt).reset()
                    }
                }
                node.opacity = 255
            }
            node.x = x

            // 应用斜坡效果
            this.applySlopeEffect(node, x)
        }
        for (let i = childIndex; i < loopNode.children.length; i++) loopNode.children[i].opacity = 0
    }

    protected getCameraInfo(bgNode) {
        let zoomRatio = this.camera.zoomRatio
        let lb = ut.convertToNodeAR(this.camera.node, bgNode, cc.v2(-cc.winSize.width * 0.5 / zoomRatio, -cc.winSize.height * 0.5 / zoomRatio))
        let rt = ut.convertToNodeAR(this.camera.node, bgNode, cc.v2(cc.winSize.width * 0.5 / zoomRatio, cc.winSize.height * 0.5 / zoomRatio))
        return { lb, rt }
    }

    protected moveCloud(cloud: cc.Node, dt) {
        let loopNode = cloud.Child('loop')
        if (cloud.children.length < 2) {
            let node = cc.instantiate2(loopNode, cloud)
            node.x += loopNode.width
        }
        cloud.x -= dt * CLOUD_MOVE_SPEED
        if (cloud.x < -loopNode.width) {
            cloud.x += loopNode.width
        }
    }

    private async loadUI() {
        let prefab = await assetsMgr.loadTempRes(`planet/ui`, cc.Prefab, this.getTag())
        let ui = cc.instantiate2(prefab, this.rootNode_)
        ui.setSiblingIndex(this.topLayerNode_.getSiblingIndex()) //插到后一位
        this.uiNode = ui
        this.qteNode = ui.Child('QTE')
        this.hitTipsNode = ui.Child('hitTips')
        this.qteNode.active = false
        this.hitTipsNode.active = false
    }

    protected async loadMapPrefabs() {
        let prefabs = [
            { node: this.heroNode_, name: "hero", isNode: true },
            { node: this.nodeRewardNode_, name: "nodeReward" },
        ]
        let nodes: PlanetNodeModel[] = this.getCurMap().getUnCompleteNodes()
        let map = {}
        for (let node of nodes) {
            map[node.nodeType] = true
        }
        // if (map[PlanetNodeType.CHECK_POINT]) {
        prefabs.push({ node: this.checkPointNode_, name: "checkPoint" })
        // }
        // if (map[PlanetNodeType.MINE]) {
        prefabs.push({ node: this.mineNode_, name: "mine" })
        // }
        if (map[PlanetNodeType.QUESTION]) {
            prefabs.push({ node: this.questionNode_, name: "question" })
        }
        await ut.promiseMap(prefabs, async ({ name, isNode }) => {
            let prefab = await assetsMgr.loadTempRes(`planet/${name}`, cc.Prefab, this.getTag())
            if (isNode) {
                this[`${name}Node_`] = cc.instantiate2(prefab, this.mapNode_)
            }
            else {
                this[`${name}Node_`] = prefab
            }
        })
    }

    public onEnter() {
        cc.find("Canvas/PlanetCamera").active = true
        viewHelper.showPnl("common/PlanetUI")
        viewHelper.showPnl("planet/PlanetTipsPnl")
        this.changePlanetMap(this.getCurMap())
    }

    protected onReload() {
        if (gameHelper.guide.isWorking()) return
        eventCenter.emit(CoreEventType.RELOAD_WIND)
    }

    protected async initNodes() {
        this.cleanViewNodes()
        let map = this.getCurMap()
        let nodes = map.getNodes()
        nodes = nodes.slice(map.getProgress())

        return this.updateNextNodes()
    }

    protected getCurMap() {
        return this.model.getBranchCurMap()
    }

    protected getCurMapId() {
        return this.getCurMap()?.getId()
    }

    protected async updateNextNodes(sync: boolean = false) {
        let map = this.getCurMap()
        let curIndex = map.getProgress()
        let viewIndex = Math.max(curIndex, this.viewIndex)
        let dis = viewIndex - curIndex
        let minDis = 5
        if (dis > minDis) {
            return
        }

        let nodes = map.getNodes()
        nodes = nodes.slice(viewIndex, viewIndex + minDis)
        this.viewIndex = viewIndex + minDis
        for (let node of nodes) {
            let viewNode
            if (node instanceof PlanetMineModel) {
                viewNode = this.initMine(node)
            }
            else if (node instanceof PlanetCheckPointModel) {
                viewNode = this.initCheckPoint(node)
            }
            else if (node instanceof PlanetEmptyNode) {
                viewNode = await this.initEmptyNode(node)
            }
            else if (node instanceof PlanetQuestionModel) {
                viewNode = this.initQuestion(node)
            }
            if (!cc.isValid(this)) return
            if (viewNode) {
                this.viewNodes.push(viewNode)
            }
            if (!sync) {
                await ut.waitNextFrame(1, this)
            }
        }
    }

    protected cleanViewNodes() {
        this.viewNodes.forEach(v => {
            v.parent = null
            v.destroy()
        })
        this.viewNodes.length = 0
        this.viewIndex = -1
    }

    protected fixHeight() {
        this.rootNode_.scale = viewHelper.getPlanetWindScale(true)
    }

    public onLeave() {
        super.onLeave()
        cc.find("Canvas/PlanetCamera").active = false
        this.model.updateMap()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private async enterBattle(model?: PlanetCheckPointModel, extra: any = {}) {
        viewHelper.waitEnterUI('battle/BattleReady').then(() => {
            if (!cc.isValid(this)) return
            this.showUI(false)
        })
        let viewNode = this.viewNodes.find(node => cc.isValid(node) && node.Component(cc.Component)["model"] == model)
        let pos = null
        if (viewNode) {
            let camera = viewHelper.getPlanetCamera()
            pos = cc.v2()
            let worldPos = viewNode.convertToWorldSpaceAR(pos)
            camera.getWorldToScreenPoint(worldPos, pos)
            pos.y -= cc.winSize.height * 0.5
        }
        let onWin = async (res) => {
            return model.syncDie(res)
        }
        let map = this.getCurMap()
        let planet = gameHelper.planet.getCurPlanet()
        let branch = this.getCurMap().getBranch()
        let battleBg
        if (branch?.battleBg) {
            battleBg = resHelper.getBattleBg(branch.battleBg)
        }
        let levelType = BattleLevelType.MAIN
        if (branch?.id == "1007-1") {
            levelType = BattleLevelType.SPACE_STONE
        }
        let levelId = model.getId()

        let title = planet.name
        if (branch) {
            title = branch.name || title
        }
        else {
            let area = map.getArea()
            title = area.name || title
        }
        let progress = assetsMgr.lang("name_chapter_planet", model.getLevelIndex())

        let isWin = model.isWin
        let res: any = await viewHelper.showBattle({
            checkPoint: model, onWin, title: { name: title, progress }, battleBg,
            levelType, levelId, isWin,
        })
        if (res?.isWin) {
            if (!extra.debug && model) {
                model.die()
                model.end()
            }
        }
        if (extra.debug) {
            this.emit(EventType.EXIT_BATTLE)
        }
        else {
            gameHelper.hero.battleEnd()
        }
    }

    private exitBattle(model: PlanetCheckPointModel) {
        let opt: any = { lerp: false }
        this.showUI(true)
        this.focusHero(opt)
    }

    private async enterQuestion(model: PlanetQuestionModel) {
        let end = () => {
            gameHelper.hero.questionEnd()
        }

        let succ = await new Promise((r) => {
            viewHelper.showPnl("planetQuestion/PlanetQuestionPreviewPnl", { question: model, callback: r })
        })

        if (!succ) {
            return end()
        }

        this.showUI(false)

        let resultUrl = "planetQuestion/PlanetQuestionResultPnl"
        let p = viewHelper.preloadPnl(resultUrl)

        succ = await new Promise((r) => {
            viewHelper.showPnl("planetQuestion/PlanetQuestionPnl", model, r)
        })

        let isDone = false
        if (succ) {
            const ary = this.getCurMap().getNodes()
            isDone = ary[ary.length - 1] == model
        }
        await p
        await new Promise((r) => {
            viewHelper.showPnl(resultUrl, { isWin: succ, isDone, callback: r, rewards: model.rewards })
        })

        if (succ) {
            model.die()
            model.end()
        }

        end()
    }

    private exitQuestion() {
        if (this.getCurMap().isDone()) {
            return void viewHelper.gotoPlanetEntry()
        }
        this.showUI(true)
    }

    private showUI(show) {
        this.mapNode_.opacity = show ? 255 : 0
        this.uiNode.opacity = show ? 255 : 0
        this.topLayerNode_.opacity = show ? 255 : 0
        viewHelper.showUI(show)
    }

    protected async initHero() {
        this.hero = gameHelper.hero
        this.heroCmpt.init()
    }

    protected initMine(model: PlanetMineModel) {
        let node = cc.instantiate2(this.mineNode_, this.mapNode_)
        node.active = true
        node.Component(PlanetMineCmpt).init(model)
        return node
    }

    protected initCheckPoint(model: PlanetCheckPointModel) {
        let node = cc.instantiate2(this.checkPointNode_, this.mapNode_)
        node.active = true
        node.Component(PlanetCheckPointCmpt).init(model)
        return node
    }

    protected async initEmptyNode(model: PlanetEmptyNode) {
        return this.initSpNode(model, model.prefab)
    }

    protected async initSpNode(model: PlanetNodeModel, prefabName: string) {
        let path = `planet/${this.model.getId()}/`
        if (model.getMap().getBranch()) {
            path += "branch/"
        }
        path += `${prefabName}`
        let fullPath = `tmp/prefab/` + path
        if (!assetsMgr.getInfoWithPath("resources", fullPath)) {
            path = `planet/sp/${prefabName}`
        }
        let prefab = await assetsMgr.loadTempRes(path, cc.Prefab, this.getTag())
        if (!cc.isValid(this)) return
        let node = cc.instantiate2(prefab, this.mapNode_)
        let cmpt: any = node.getComponent(cc.Component)
        if (!cmpt) {
            twlog.error(`${path} 没有组件`)
        }
        cmpt.init(model, this)
        node.setPosition(model.position)
        return node
    }

    protected initQuestion(model: PlanetQuestionModel) {
        let node = cc.instantiate2(this.questionNode_, this.mapNode_)
        node.active = true
        node.Component(PlanetNodeCmpt).init(model)
        return node
    }

    private onTouchEnd(event: cc.Event.EventTouch) {
    }

    protected update(dt) {
        this.updateCamera(dt)
        this.updateBg(dt)
    }

    private enterCollect(model: PlanetMineModel) {
        if (model.qteId) {
            this.qteNode.active = true
            this.qteNode.Component(PlanetQTECmpt).init(model)

            let mineNode = this.viewNodes.find(node => cc.isValid(node) && node.Component(PlanetMineCmpt)?.model == model)
            if (!mineNode) return
            let cmpt = mineNode.Component(PlanetMineCmpt)
            cmpt.loadedPromise.then(() => {
                let pos = cc.v2(265, -325)
                if (model.size.height < this.qteNode.height) {
                    pos.y = this.qteNode.height * 0.5 - model.size.height
                }
                else pos.y = -model.size.height * 0.5
                pos.x = model.size.width * 0.5 + 30
                pos = ut.convertToNodeAR(mineNode.Child('ui'), this.qteNode.parent, pos)
                this.qteNode.setPosition(pos)
            })
        }
    }

    private exitCollect() {
        this.qteNode.active = false
    }

    protected updateCamera(dt) {
        let cameraTarget = this.cameraTarget
        if (!cameraTarget) return

        let camPosition = cc.v2()
        this.camera.node.getPosition(camPosition)

        let targetPos: cc.Vec2 = null
        if (cameraTarget.pos) {
            targetPos = cameraTarget.pos
        }
        else if (cameraTarget.posFunc) {
            targetPos = cameraTarget.posFunc()
        }

        if (camPosition.equals(targetPos)) {
            this.cameraMoveEnd()
            return
        }
        else {
            this.cameraMoveStart()
        }

        let speed = cameraTarget.speed || 10
        let lerp = cameraTarget.lerp

        if (camPosition.sub(targetPos).mag() < 2 || lerp === false) {
            camPosition.set(targetPos)
            cameraTarget.lerp = false
        } else {
            camPosition.set(camPosition.lerp(targetPos, cc.misc.clamp01(speed * dt)))
        }
        this.camera.node.x = camPosition.x
        this.camera.node.y = camPosition.y

    }

    public cameraMoveStart() {
        if (!this.waitCameraStopCallback) {
            this.waitCameraStopPromise = new Promise(r => this.waitCameraStopCallback = r)
            eventCenter.emit(EventType.CAMERA_MOVE_START, this.camera)
        }
    }

    public cameraMoveEnd() {
        if (this.waitCameraStopCallback) {
            this.waitCameraStopCallback()
            this.waitCameraStopCallback = null
            eventCenter.emit(EventType.CAMERA_MOVE_END, this.camera)
        }
    }

    protected async changePlanetMap(map: PlanetMap, lastMap?: any) {
        let p
        if (!!lastMap) {
            let info = { cb: (done, tot) => { } }
            let time = 1
            p = cc.tween(this.node).progress(1, (elapsed) => {
                info.cb(elapsed, time)
            }).promise()
            eventCenter.emit(CoreEventType.LOAD_BEGIN_WIND, info)
        }
        this.onChangeMap(map, lastMap)
        await this.initNodes()
        if (!!lastMap) {
            await p
            this.emit(CoreEventType.LOAD_END_WIND);
        }
        this.initHero()

        if (map.needLandAnim()) {
            await this.initFirstLand()
        }
        this.initCameraPos()
        this.focusHero()
    }

    protected initCameraPos() {
        let map = this.getCurMap()
        let preNode = map.getPreNode()
        let pos = cc.v2()
        if (preNode && !(preNode instanceof PlanetEmptyNode)) {
            pos = ut.convertToNodeAR(this.mapNode_, this.camera.node.parent, this.getNodeCameraPos(preNode))
        }
        else {
            pos = ut.convertToNodeAR(this.mapNode_, this.camera.node.parent, this.heroNode_.getPosition())
        }
        let bounding = this.getCameraBounding()
        pos.x = cc.misc.clampf(pos.x, bounding.xMin, bounding.xMax)
        this.camera.node.x = pos.x
        return pos
    }

    protected onChangeMap(map, lastMap) {
        this.bgSwitchNode_.Swih(`bg-${map.getId()}`)
        this.mapNode_.width = map.getWidth()
    }

    private async onNodeComplete(planetId, node: PlanetNodeModel) {
        this.viewNodes = this.viewNodes.filter(node => cc.isValid(node))
        this.dropItems = this.dropItems.filter(node => cc.isValid(node))

        let map = node.getMap()

        if (!map.isDone()) {
            this.updateNextNodes()
        }

        if (node instanceof PlanetMineModel || node instanceof PlanetEmptyNode) {
            await this.genRewardNodesByMine(node)
        }

        await unlockHelper.checkAndShow()

        if (map.getArea()?.isDone()) {
            this.onDone(node)
        }
    }

    private async onDone(node: PlanetNodeModel) {
        let map = node.getMap()
        if (map.getBranch()) return
        if (!this.model.immediateDone) return

        await new Promise(r => {
            if (this.model.isDone()) {
                viewHelper.showPnl('common/PlanetFinishRewardPnl', null, r)
            }
            else {
                viewHelper.showPnl("planet/PlanetAreaDonePnl", map.getArea(), r)
            }
        })
        viewHelper.gotoPlanetEntry()
    }

    private stopActGenReward() {
        animHelper.planetGenStopAll()
    }

    private async genRewardNodesByMine(mine: PlanetMineModel | PlanetEmptyNode) {
        if (mine.noShowReward) return
        return this.genRewardNodes(mine.rewards, mine["centerPos"] || mine.position, mine)
    }

    private async genRewardNodes(rewards: ConditionObj[], center?: cc.Vec2, model?) {
        rewards = rewards.filter(c => c.type != ConditionType.WORLD_TIME && c.type != ConditionType.PASSENGER)
        if (rewards.length <= 0) return
        rewards = gameHelper.mergeCondition(rewards)

        let recordPosAry = []
        let gen = (reward) => {
            let node = cc.instantiate2(this.nodeRewardNode_, this.mapNode_)
            let cmpt = node.Component(PlanetNodeRewardCmpt)
            cmpt.init(reward)

            animHelper.playPlanetRewardJump(node, center, recordPosAry).then(() => {
                this.dropItems.push(node)
                cmpt.onDropEnd()
            })
            return node
        }

        let items: cc.Node[] = []
        for (let reward of rewards) {
            let iconNum = reward.extra?.iconNum || reward.num
            iconNum = Math.min(iconNum, 5)
            for (let i = 0; i < iconNum; i++) {
                let cond = new ConditionObj().init(reward)
                cond.num = 1
                let node = gen(cond)
                items.push(node)
            }
        }
        let go = (items: cc.Node[]) => {
            if (items.length <= 0) return
            let node = items[0]
            let cmpt = node.addComponent(PlanetNodeRewardGroupCmpt)
            return new Promise((r) => {
                cmpt.init(items, r, model)
            })
        }
        go(items)
    }

    public setCameraTarget(target) {
        // console.warn("setCameraTarget", target)
        this.cameraTarget = target
    }

    public getCameraTaget() {
        return this.cameraTarget
    }

    public focusHero(opt: any = {}) {
        let targetPos = cc.v2()
        let bounding = this.getCameraBounding()
        let hero = gameHelper.hero

        let curNode, preHeroPos, nextHeroPos, preCameraPos, curCameraPos
        let update = () => {
            let map = this.model.getBranchCurMap()
            curNode = map.getCurNode()
            if (!curNode) return
            preHeroPos = ut.convertToNodeAR(this.heroNode_, this.mapNode_)
            nextHeroPos = this.getNodeHeroPos(curNode)

            preCameraPos = this.camera.getPosition()

            curCameraPos = ut.convertToNodeAR(this.mapNode_, this.camera.node.parent, this.getNodeCameraPos(curNode))
        }
        update()

        let boundDeal = () => {
            if (!opt?.back && targetPos.x < preCameraX) {
                targetPos.x = preCameraX
            }
            targetPos.x = cc.misc.clampf(targetPos.x, bounding.xMin, bounding.xMax)
            preCameraX = targetPos.x
            targetPos.y = this.camera.node.y
            return targetPos
        }

        let preCameraX = this.camera.node.x
        let posFunc = () => {
            if (curNode != this.getCurMap()?.getCurNode()) {
                update()
            }
            if (!curNode) {
                return this.camera.getPosition(targetPos)
            }
            let ratio = (this.heroNode_.x - preHeroPos.x) / (nextHeroPos.x - preHeroPos.x)
            if (isNaN(ratio)) {
                ratio = 1
            }
            let preX = preCameraPos?.x || 0
            let curX = curCameraPos?.x || this.camera.node.x
            targetPos.x = cc.misc.lerp(preX, curX, cc.misc.clamp01(ratio))
            return boundDeal()
        }
        this.setCameraTarget(Object.assign({ posFunc, lerp: false }, opt))
    }

    protected getNodeHeroPos(node: PlanetNodeModel) {
        if (!node) return
        if (node.endPos) return node.endPos
        return gameHelper.hero.getPosByTarget(node).endPos
    }

    protected getNodeCameraPos(node: PlanetNodeModel) {
        if (!node) return
        if (node.endPos) return node.endPos
        if (node instanceof PlanetMineModel) {
            return node.centerPos
        }
        return node.position
    }

    public getHeroNode() {
        return this.heroNode_
    }

    public getCamera() {
        return this.camera
    }

    public getCameraBounding(zoomRatio: number = 1) {
        let mapNode = this.mapNode_
        let camera = this.camera
        let maxPos = ut.convertToNodeAR(mapNode, camera.node.parent, cc.v2(mapNode.width))
        let cameraWidth = cc.winSize.width / zoomRatio
        let width = maxPos.x - cameraWidth / 2
        return cc.rect(0, 0, width, MAX_VALUE)
    }

    protected getCurGuideNode() {
        let curNode = this.getCurMap().getCurNode()
        let viewNode = this.viewNodes.find(node => cc.isValid(node) && node.Component(cc.Component)["model"] == curNode)
        if (!viewNode) return
        if (viewNode.Component(PlanetNodeCmpt)) {
            return viewNode.Component(PlanetNodeCmpt)["touchNode"] || viewNode
        }
        return viewNode
    }

    protected async initFirstLand() {
        viewHelper.showUI(false)
        let map = this.getCurMap()
        this.camera.zoomRatio = 1.3
        let orgY = this.camera.node.y
        this.camera.node.y = orgY - 120
        this.camera.node.x = 0

        await ut.wait(0.5, this) //配合loading动画结束
        viewHelper.showPnl("planet/PlanetLandAnimPnl", map)
        let time = 3
        cc.tween(this.camera).to(time, { zoomRatio: 1 }).start()
        cc.tween(this.camera.node).to(time, { y: orgY }).start()
        await ut.wait(time, this)
    }

    //大型植物上飘距离变长
    private showHitTips(damage, damageMul: number, model: PlanetMineModel, node?, isAuto = false) {
        let hitTips = this.hitTipsNode
        let prefab
        let mineNode = this.viewNodes.find(node => cc.isValid(node) && node.Component(PlanetMineCmpt)?.model == model)
        if (!mineNode) mineNode = node
        if (!mineNode) return
        hitTips.active = true
        let isQte = !!model.qteId
        if (damageMul == 0) {
            prefab = hitTips.Child('miss')
        }
        else if (!isQte) {
            prefab = hitTips.Child('lb')
        }
        else { //qte
            if (damageMul == 1) {
                prefab = hitTips.Child('lbS')
            }
            else if (damageMul == 2) {
                prefab = hitTips.Child('lbM')
            }
            else {
                prefab = hitTips.Child('lbL')
            }
        }

        hitTips.setPosition(ut.convertToNodeAR(mineNode.Child('ui'), hitTips.parent))

        let tipNode = cc.instantiate2(prefab, hitTips);

        const randomOffsetX = 30
        tipNode.x = ut.randomRange(-randomOffsetX, randomOffsetX)
        tipNode.y = 90
        if (isAuto) {
            let p = ut.convertToNodeAR(mineNode, tipNode.parent, cc.v2(0, model.size.height * 0.5))
            tipNode.y = p.y
        }
        tipNode.name = 'tip'
        tipNode.active = true

        let isCirt = false
        if (damageMul) {
            if (damage >= 0) {
                tipNode.Component(cc.Label).string = "-" + damage
                if (damageMul > 1 && !isQte) {
                    isCirt = true
                    tipNode.SetColor('#F7A221')
                }
            }
            else if (damage < 0) {
                tipNode.Component(cc.Label).string = "+" + Math.abs(damage)
                tipNode.SetColor('41c82f')
                tipNode.y = -45
            }
        }

        let startY = tipNode.y

        if (isCirt) {
            let scale = 1.3
            tipNode.scale = scale
            tipNode.y += 5
            // cc.tween(tipNode)
            //     .to(0.12, { y: startY + 40, scale: 3 }, { easing: cc.easing.sineOut }) 
            //     .to(0.18, { y: startY + 10, scale: 0.85 * scale }, { easing: cc.easing.sineOut })
            //     .to(0.7, { y: startY + 24, opacity: 0, scale: 1.1 * scale }, { easing: cc.easing.sineIn })
            //     .call(() => tipNode.destroy())
            //     .start()

            cc.tween(tipNode)
                .to(0.12, { scale: 3 }, { easing: cc.easing.sineOut })
                .to(0.18, { scale: 0.85 * scale }, { easing: cc.easing.sineOut })
                .to(0.1, { scale: 1.1 * scale }, { easing: cc.easing.sineIn })
                .start()

            cc.tween(tipNode)
                // .to(0.5, { y: startY + height * 0.5, opacity: 255 }, { easing: cc.easing.sineOut })
                .to(0.4, { y: startY + 35, opacity: 255 }, { easing: cc.easing.sineOut })
                // .delay(0.1)
                // .to(0.1, { y: startY + height * 0.5 + 30, opacity: 0 }, { easing: cc.easing.sineIn })
                .to(0.6, { y: startY + 55, opacity: 0 }, { easing: cc.easing.sineOut })
                .call(() => {
                    tipNode.destroy()
                }).start()
        }
        else {
            cc.tween(tipNode)
                // .to(0.5, { y: startY + height * 0.5, opacity: 255 }, { easing: cc.easing.sineOut })
                .to(0.4, { y: startY + 20, opacity: 255 }, { easing: cc.easing.sineOut })
                // .delay(0.1)
                // .to(0.1, { y: startY + height * 0.5 + 30, opacity: 0 }, { easing: cc.easing.sineIn })
                .to(0.1, { y: startY + 25, opacity: 0 }, { easing: cc.easing.sineOut })
                .call(() => {
                    tipNode.destroy()
                }).start()
        }
    }

    private onPnlEnter(pnl) {
        if (pnl.key == "tool/ToolMakePnl") {
            this.showUI(false)
        }
    }

    private onPnlLeave(pnl) {
        if (pnl.key == "tool/ToolMakePnl") {
            this.showUI(true)
        }
    }

    // ==================== 斜坡系统 ====================

    /**
     * 斜坡配置数组 - 可以配置多段斜坡
     * 每个配置对象包含：
     * - enabled: 是否启用斜坡
     * - startX: 斜坡开始X坐标
     * - endX: 斜坡结束X坐标
     * - angle: 斜坡角度（度数，正数向上倾斜，负数向下倾斜）
     * - baseY: 基础Y坐标偏移
     */
    private slopeConfigs = [
        // 示例1：第一段上坡（15度向上倾斜）
        {
            enabled: true,
            startX: 1000,
            endX: 2000,
            angle: 30,
            baseY: 0
        },
        // 示例2：第二段下坡（-10度向下倾斜）
        {
            enabled: true,
            startX: 3000,
            endX: 4000,
            angle: -30,
            baseY: 0
        }
        // 可以继续添加更多斜坡段...
    ];

    /**
     * 根据X坐标计算斜坡角度和Y偏移
     * @param x 当前X坐标
     * @returns {angle: number, y: number} 旋转角度和Y坐标偏移
     */
    private calculateSlopeTransform(x: number): {angle: number, y: number} {
        // 遍历所有斜坡配置，找到匹配的斜坡段
        for (let config of this.slopeConfigs) {
            if (!config.enabled) continue;

            if (x >= config.startX && x <= config.endX) {
                // 在斜坡范围内，计算旋转角度和Y偏移
                let angle = config.angle; // 整个斜坡段使用相同的角度

                // 根据角度计算Y偏移，让斜坡看起来连续
                let distance = x - config.startX;
                let radians = config.angle * Math.PI / 180;
                let yOffset = config.baseY + distance * Math.tan(radians);

                return { angle: angle, y: yOffset };
            }
        }

        // 如果不在任何斜坡范围内，返回默认值
        return { angle: 0, y: 0 };
    }

    /**
     * 检查指定X坐标是否在斜坡范围内
     * @param x X坐标
     * @returns 是否在斜坡范围内
     */
    private isInSlopeRange(x: number): boolean {
        return this.slopeConfigs.some(config =>
            config.enabled && x >= config.startX && x <= config.endX
        );
    }

    /**
     * 应用斜坡效果到节点（旋转+位移）
     * 在loopNode方法中调用此方法
     * @param node 要应用斜坡的节点
     * @param x 节点的X坐标
     */
    private applySlopeEffect(node: cc.Node, x: number): void {
        if (this.isInSlopeRange(x)) {
            let transform = this.calculateSlopeTransform(x);
            node.angle = transform.angle;  // 设置旋转角度
            node.y = transform.y;          // 设置Y坐标偏移
        } else {
            // 不在斜坡范围内，重置为默认状态
            node.angle = 0;
            node.y = 0;
        }
    }
}
