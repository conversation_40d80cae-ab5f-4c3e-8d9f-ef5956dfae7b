# 地图斜坡系统使用说明

## 概述

新的斜坡系统已经集成到 `PlanetWindCtrl` 中，允许你在保持预制体平行设计的同时，通过配置参数将地面转换为斜坡效果。

## 实现原理

1. **预制体保持不变**：所有地面预制体仍然是平行向右延伸的设计
2. **运行时转换**：在 `PlanetWindCtrl` 中配置斜坡参数，运行时动态计算Y坐标
3. **线性插值**：在指定的X坐标范围内，使用线性插值计算斜坡的Y坐标

## 配置位置

在 `PlanetWindCtrl.ts` 文件中找到 `slopeConfigs` 数组：

```typescript
private slopeConfigs = [
    // 示例1：第一段上坡
    {
        enabled: true,
        startX: 1000,
        endX: 2000,
        height: 300,
        baseY: 0
    },
    // 示例2：第二段下坡
    {
        enabled: true,
        startX: 3000,
        endX: 4000,
        height: -200,
        baseY: 300
    }
    // 可以继续添加更多斜坡段...
];
```

## 配置参数说明

每个斜坡配置对象包含：

- **enabled** (boolean): 是否启用这段斜坡
- **startX** (number): 斜坡开始的X坐标
- **endX** (number): 斜坡结束的X坐标
- **height** (number): 斜坡高度差（正数向上，负数向下）
- **baseY** (number): 斜坡开始时的基础Y坐标

## 使用步骤

### 1. 修改配置数组
直接在 `PlanetWindCtrl.ts` 中修改 `slopeConfigs` 数组。

### 2. 配置示例

#### 简单上坡：
```typescript
{
    enabled: true,
    startX: 500,      // 从X=500开始
    endX: 1500,       // 到X=1500结束
    height: 200,      // 向上200像素
    baseY: 0          // 从Y=0开始
}
```

#### 简单下坡：
```typescript
{
    enabled: true,
    startX: 2000,     // 从X=2000开始
    endX: 3000,       // 到X=3000结束
    height: -150,     // 向下150像素
    baseY: 200        // 从Y=200开始
}
```

#### 复杂地形（多段斜坡）：
```typescript
private slopeConfigs = [
    // 第一段：上坡
    {
        enabled: true,
        startX: 1000,
        endX: 2000,
        height: 300,
        baseY: 0
    },
    // 第二段：平台（可以不配置，或者配置height为0）
    
    // 第三段：下坡
    {
        enabled: true,
        startX: 3500,
        endX: 4500,
        height: -200,
        baseY: 300
    },
    // 第四段：再次上坡
    {
        enabled: true,
        startX: 6000,
        endX: 7000,
        height: 400,
        baseY: 100
    }
];
```

## 调试建议

1. **从简单开始**：先配置一段简单的斜坡测试效果
2. **注意坐标范围**：确保X坐标范围与你的地图实际大小匹配
3. **保证连续性**：相邻斜坡段的结束Y坐标应该与下一段的开始Y坐标匹配
4. **实时调试**：可以在运行时修改配置数组的值来快速测试效果

## 技术细节

- 斜坡效果在每个循环节点的 `loopNode` 方法中自动应用
- 只有在斜坡范围内的节点才会被调整Y坐标
- 不在任何斜坡范围内的节点保持Y=0（原始位置）

## 禁用斜坡

如果要临时禁用所有斜坡效果，可以：
1. 将所有配置的 `enabled` 设为 `false`
2. 或者将整个 `slopeConfigs` 数组设为空：`private slopeConfigs = [];`
